---
output:
  html_document: default
  word_document: default
  pdf_document:
    keep_tex: true
    extra_dependencies: ["graphicx", "float", "tikz", "xcolor"]

# Metadatos ICFES
icfes:
  competencia:
    - interpretacion_representacion
  nivel_dificultad: 2
  contenido:
    categoria: estadistica
    tipo: generico
  contexto: comunitario
  eje_axial: eje4
  componente: aleatorio
---

```{r setup, include=FALSE}
# Configuración para todos los formatos de salida
Sys.setlocale(category = "LC_NUMERIC", locale = "C")
options(OutDec = ".")

# Configurar el motor LaTeX globalmente
options(tikzLatex = "pdflatex")
options(tikzXelatex = FALSE)
options(tikzLatexPackages = c(
  "\\usepackage{tikz}",
  "\\usepackage{colortbl}",
  "\\usepackage{xcolor}",
  "\\usepackage{graphicx}",
  "\\usepackage{float}"
))

library(exams)
library(reticulate)
library(digest)
library(testthat)
library(knitr)
library(stringr)

typ <- match_exams_device()
options(scipen = 999)
knitr::opts_chunk$set(
  warning = FALSE,
  message = FALSE,
  fig.showtext = FALSE,
  fig.cap = "",
  fig.keep = 'all',
  dev = c("png", "pdf"),
  dpi = 150,
  fig.pos = "H"
)

# Configuración para chunks de Python
knitr::knit_engines$set(python = function(options) {
  knitr::engine_output(options, options$code, '')
})

# Asegurar que Python esté correctamente configurado
use_python(Sys.which("python"), required = TRUE)

# Semilla aleatoria para diversidad de versiones
set.seed(sample(1:100000, 1))
```

```{r data_generation, echo=FALSE, results="hide"}
# Función principal de generación de datos para competencia INTERPRETACIÓN Y REPRESENTACIÓN
generar_datos <- function() {
  # Contextos deportivos aleatorios ampliados para mayor diversidad
  contextos_deportivos <- list(
    list(evento = "Juegos Panamericanos", region = "América", articulo = "los"),
    list(evento = "Juegos Olímpicos", region = "mundial", articulo = "los"),
    list(evento = "Campeonato Mundial de Atletismo", region = "mundial", articulo = "el"),
    list(evento = "Juegos Centroamericanos", region = "Centroamérica", articulo = "los"),
    list(evento = "Juegos Suramericanos", region = "Suramérica", articulo = "los"),
    list(evento = "Juegos Mediterráneos", region = "Mediterráneo", articulo = "los"),
    list(evento = "Juegos Asiáticos", region = "Asia", articulo = "los"),
    list(evento = "Juegos Africanos", region = "África", articulo = "los"),
    list(evento = "Juegos Europeos", region = "Europa", articulo = "los"),
    list(evento = "Juegos del Commonwealth", region = "Commonwealth", articulo = "los")
  )
  
  contexto_sel <- sample(contextos_deportivos, 1)[[1]]
  
  # Generar 5 años consecutivos aleatorios (entre 2010-2023)
  año_inicial <- sample(2010:2019, 1)
  años <- seq(año_inicial, año_inicial + 16, by = 4)[1:5]  # Cada 4 años como eventos deportivos reales
  
  # Generar datos realistas para cada año
  datos_años <- list()
  
  for(i in 1:5) {
    # Países participantes (variación realista)
    paises_base <- sample(35:50, 1)
    paises <- pmax(30, paises_base + sample(-3:3, 1))
    
    # Deportes (variación menor)
    deportes_base <- sample(35:45, 1)
    deportes <- pmax(30, deportes_base + sample(-2:2, 1))
    
    # Total de atletas (variación más amplia)
    atletas_base <- sample(4000:8000, 1)
    atletas <- pmax(3000, atletas_base + sample(-500:500, 1))
    
    datos_años[[i]] <- list(
      año = años[i],
      paises = paises,
      deportes = deportes,
      atletas = atletas
    )
  }
  
  # Seleccionar año para el cálculo (último año)
  año_calculo <- datos_años[[5]]
  
  # Disciplinas deportivas y porcentajes
  disciplinas <- c("natación", "atletismo", "gimnasia", "ciclismo", "boxeo", 
                   "judo", "taekwondo", "esgrima", "tiro", "remo")
  disciplina_sel <- sample(disciplinas, 1)
  
  # Porcentaje entre 5% y 10%
  porcentaje <- sample(5:10, 1)
  porcentaje_decimal <- porcentaje / 100
  
  # Calcular número correcto de atletas en la disciplina
  atletas_disciplina <- round(año_calculo$atletas * porcentaje_decimal)
  
  # SISTEMA AMPLIADO DE DISTRACTORES para competencia INTERPRETACIÓN Y REPRESENTACIÓN
  afirmaciones <- list()
  
  # AFIRMACIÓN CORRECTA
  afirmacion_correcta <- paste0("suficiente para determinar el número de atletas que participó en ", 
                               disciplina_sel, " en el año ", año_calculo$año)
  
  # DECISIÓN ALEATORIA: ¿Permitir valores duplicados con justificaciones diferentes?
  # 30% de probabilidad de generar opciones con misma conclusión pero diferentes justificaciones
  permitir_valores_duplicados <- sample(c(TRUE, FALSE), 1, prob = c(0.3, 0.7))
  
  # DISTRACTORES PRINCIPALES (errores conceptuales comunes)
  afirmaciones_incorrectas <- c()
  
  # DISTRACTOR 1: Error de pensar que se necesita multiplicar por países
  afirmaciones_incorrectas <- c(afirmaciones_incorrectas,
    paste0("insuficiente, porque falta multiplicar el resultado por el número de países participantes en ", año_calculo$año))
  
  # DISTRACTOR 2: Confundir porcentaje decimal (0.07 vs 0.7)
  porcentaje_incorrecto <- porcentaje * 10  # 7% se convierte en 70%
  afirmaciones_incorrectas <- c(afirmaciones_incorrectas,
    paste0("incorrecto, pues se debe multiplicar ", porcentaje_incorrecto/100, " por el número de atletas de ", año_calculo$año))
  
  # DISTRACTOR 3: Malentendido sobre números exactos
  afirmaciones_incorrectas <- c(afirmaciones_incorrectas,
    paste0("correcto, solamente si el resultado obtenido es un número exacto de atletas en ", disciplina_sel))
  
  # DISTRACTORES ADICIONALES para mayor diversidad
  afirmaciones_incorrectas <- c(afirmaciones_incorrectas,
    paste0("insuficiente, porque se debe dividir entre el número de deportes practicados en ", año_calculo$año),
    paste0("incorrecto, porque se debe calcular el promedio de atletas por país participante"),
    paste0("correcto, pero solo si se redondea al número entero más cercano de competidores"),
    paste0("insuficiente, porque falta considerar el número total de deportes en el evento"))
  
  # JUSTIFICACIONES ALTERNATIVAS para permitir valores duplicados
  if(permitir_valores_duplicados) {
    # Justificaciones alternativas para "suficiente" (valor correcto con razonamiento diferente)
    justificaciones_alternativas_correctas <- c(
      paste0("suficiente, porque el ", porcentaje, "% representa la proporción exacta de atletas en ", disciplina_sel),
      paste0("suficiente, ya que multiplicar ", porcentaje_decimal, " por ", año_calculo$atletas, " da el número buscado"),
      paste0("suficiente para calcular los atletas de ", disciplina_sel, " en ", año_calculo$año)
    )
    
    # Justificaciones alternativas para "incorrecto" 
    justificaciones_alternativas_incorrectas <- c(
      paste0("incorrecto, porque no se considera la variación entre años del evento"),
      paste0("incorrecto, pues se debe usar el promedio de atletas de los cinco años mostrados"),
      paste0("incorrecto, ya que falta ajustar por el número de países en ", año_calculo$año)
    )
    
    afirmaciones_incorrectas <- c(afirmaciones_incorrectas, 
                                 justificaciones_alternativas_correctas,
                                 justificaciones_alternativas_incorrectas)
  }
  
  # Eliminar duplicados y valores NA
  afirmaciones_incorrectas <- unique(afirmaciones_incorrectas[!is.na(afirmaciones_incorrectas)])
  
  # SELECCIÓN ESTRATÉGICA DE 3 DISTRACTORES
  afirmaciones_incorrectas_sel <- c()
  
  if(permitir_valores_duplicados && length(afirmaciones_incorrectas) >= 3) {
    # Buscar afirmaciones que contengan "suficiente" (mismo tipo de conclusión que la correcta)
    afirmaciones_suficiente <- afirmaciones_incorrectas[grepl("suficiente", afirmaciones_incorrectas)]
    afirmaciones_otras <- afirmaciones_incorrectas[!grepl("suficiente", afirmaciones_incorrectas)]
    
    # Incluir 1 con "suficiente" si existe, completar con otras
    if(length(afirmaciones_suficiente) > 0) {
      afirmaciones_incorrectas_sel <- c(afirmaciones_incorrectas_sel, 
                                       sample(afirmaciones_suficiente, 1))
    }
    
    # Completar hasta 3 con otras afirmaciones
    while(length(afirmaciones_incorrectas_sel) < 3 && length(afirmaciones_otras) > 0) {
      candidato <- sample(afirmaciones_otras, 1)
      if(!candidato %in% afirmaciones_incorrectas_sel) {
        afirmaciones_incorrectas_sel <- c(afirmaciones_incorrectas_sel, candidato)
      }
      afirmaciones_otras <- setdiff(afirmaciones_otras, candidato)
    }
  } else {
    # Modo tradicional: seleccionar 3 distractores diferentes
    afirmaciones_incorrectas_sel <- sample(afirmaciones_incorrectas, min(3, length(afirmaciones_incorrectas)))
  }
  
  # Asegurar que tenemos exactamente 3 distractores
  if(length(afirmaciones_incorrectas_sel) < 3) {
    distractores_fallback <- c(
      paste0("insuficiente, porque falta multiplicar por el número de países de ", año_calculo$año),
      paste0("incorrecto, pues se debe multiplicar ", porcentaje/10, " por el número de atletas"),
      paste0("correcto, solo si el resultado es un número entero de atletas")
    )
    
    while(length(afirmaciones_incorrectas_sel) < 3) {
      candidato <- sample(distractores_fallback, 1)
      if(!candidato %in% afirmaciones_incorrectas_sel && candidato != afirmacion_correcta) {
        afirmaciones_incorrectas_sel <- c(afirmaciones_incorrectas_sel, candidato)
      }
    }
  }
  
  # Crear las 4 opciones finales
  todas_afirmaciones <- c(afirmacion_correcta, afirmaciones_incorrectas_sel[1:3])
  
  # Verificar que las 4 opciones sean textualmente únicas
  if(length(unique(todas_afirmaciones)) != 4) {
    todas_afirmaciones <- c(
      afirmacion_correcta,
      paste0("insuficiente, porque falta multiplicar por el número de países participantes"),
      paste0("incorrecto, pues se debe multiplicar ", porcentaje/10, " por el número de atletas"),
      paste0("correcto, solamente si el resultado es un número exacto de atletas")
    )
  }
  
  # Mezclar las opciones
  opciones_mezcladas <- sample(todas_afirmaciones)
  
  # Identificar posición correcta
  pos_correcta <- which(opciones_mezcladas == afirmacion_correcta)
  
  return(list(
    contexto = contexto_sel,
    datos_años = datos_años,
    año_calculo = año_calculo,
    disciplina = disciplina_sel,
    porcentaje = porcentaje,
    porcentaje_decimal = porcentaje_decimal,
    atletas_disciplina = atletas_disciplina,
    afirmacion_correcta = afirmacion_correcta,
    opciones = opciones_mezcladas,
    pos_correcta = pos_correcta,
    permitir_duplicados = permitir_valores_duplicados
  ))
}

# Generar datos del ejercicio
datos <- generar_datos()

# Extraer variables individuales para facilitar uso
contexto <- datos$contexto
datos_años <- datos$datos_años
año_calculo <- datos$año_calculo
disciplina <- datos$disciplina
porcentaje <- datos$porcentaje
porcentaje_decimal <- datos$porcentaje_decimal
atletas_disciplina <- datos$atletas_disciplina
afirmacion_correcta <- datos$afirmacion_correcta
opciones <- datos$opciones
pos_correcta <- datos$pos_correcta
```

```{r version_diversity_test, echo=FALSE, results="hide"}
# Prueba de diversidad de versiones para competencia INTERPRETACIÓN Y REPRESENTACIÓN
test_that("Prueba de diversidad de versiones", {
  versiones <- list()
  for(i in 1:100) {  # Reducido para compilación más rápida
    datos_test <- generar_datos()
    versiones[[i]] <- digest::digest(datos_test)
  }

  n_versiones_unicas <- length(unique(versiones))
  expect_true(n_versiones_unicas >= 80,  # Ajustado proporcionalmente
              info = paste("Solo se generaron", n_versiones_unicas,
                          "versiones únicas. Se requieren al menos 80."))
})

test_that("Prueba de coherencia matemática", {
  for(i in 1:20) {
    datos_test <- generar_datos()

    # Verificar que los datos están en rangos realistas
    expect_true(all(sapply(datos_test$datos_años, function(x) x$paises >= 30 && x$paises <= 60)))
    expect_true(all(sapply(datos_test$datos_años, function(x) x$deportes >= 30 && x$deportes <= 50)))
    expect_true(all(sapply(datos_test$datos_años, function(x) x$atletas >= 3000 && x$atletas <= 9000)))

    # Verificar que el porcentaje está en rango correcto
    expect_true(datos_test$porcentaje >= 5 && datos_test$porcentaje <= 10)

    # Verificar que tenemos exactamente 4 opciones únicas
    expect_equal(length(unique(datos_test$opciones)), 4)

    # Verificar que la respuesta correcta está presente
    expect_true(datos_test$afirmacion_correcta %in% datos_test$opciones)
  }
})

test_that("Prueba del sistema avanzado de distractores", {
  for(i in 1:30) {
    datos_test <- generar_datos()

    # Verificar opciones textualmente únicas
    expect_equal(length(unique(datos_test$opciones)), 4,
                info = "Las 4 opciones deben ser textualmente diferentes")

    # Verificar que la respuesta correcta contiene "suficiente"
    expect_true(grepl("suficiente", datos_test$afirmacion_correcta),
               info = "La respuesta correcta debe indicar que el procedimiento es suficiente")

    # Verificar diversidad en tipos de distractores
    opciones_texto <- paste(datos_test$opciones, collapse = " ")
    tiene_insuficiente <- grepl("insuficiente", opciones_texto)
    tiene_incorrecto <- grepl("incorrecto", opciones_texto)

    expect_true(tiene_insuficiente || tiene_incorrecto,
               info = "Debe haber diversidad en tipos de distractores")
  }
})
```

```{r generar_tabla_datos, echo=FALSE, results="hide"}
# Preparar datos para Python de manera más simple
evento_nombre <- contexto$evento
años_vector <- sapply(datos_años, function(x) x$año)
paises_vector <- sapply(datos_años, function(x) x$paises)
deportes_vector <- sapply(datos_años, function(x) x$deportes)
atletas_vector <- sapply(datos_años, function(x) x$atletas)

# Generar tabla de datos deportivos usando Python/matplotlib
codigo_python <- paste0("
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import numpy as np

# Datos desde R
evento = '", evento_nombre, "'
años = [", paste(años_vector, collapse = ", "), "]
paises = [", paste(paises_vector, collapse = ", "), "]
deportes = [", paste(deportes_vector, collapse = ", "), "]
atletas = [", paste(atletas_vector, collapse = ", "), "]

# Preparar datos de la tabla
datos_tabla = []
for i in range(len(años)):
    fila = [str(años[i]), str(paises[i]), str(deportes[i]), f'{atletas[i]:,}'.replace(',', '.')]
    datos_tabla.append(fila)

encabezados = ['Año', 'Países', 'Deportes', 'Total de Atletas']

# Configurar la figura
fig, ax = plt.subplots(figsize=(10, 6))
ax.axis('tight')
ax.axis('off')

# Crear la tabla
tabla = ax.table(cellText=datos_tabla,
                colLabels=encabezados,
                cellLoc='center',
                loc='center',
                bbox=[0, 0, 1, 1])

# Estilizar la tabla
tabla.auto_set_font_size(False)
tabla.set_fontsize(12)
tabla.scale(1.2, 2)

# Estilizar encabezados
for i in range(len(encabezados)):
    tabla[(0, i)].set_facecolor('#4CAF50')
    tabla[(0, i)].set_text_props(weight='bold', color='white')

# Estilizar celdas de datos
for i in range(1, len(datos_tabla) + 1):
    for j in range(len(encabezados)):
        if i % 2 == 0:
            tabla[(i, j)].set_facecolor('#f0f0f0')
        else:
            tabla[(i, j)].set_facecolor('white')

# Resaltar el último año (año de cálculo)
for j in range(len(encabezados)):
    tabla[(len(datos_tabla), j)].set_facecolor('#FFE082')
    tabla[(len(datos_tabla), j)].set_text_props(weight='bold')

plt.title(f'Datos de {evento}', fontsize=16, fontweight='bold', pad=20)
plt.tight_layout()
plt.savefig('tabla_datos_deportivos.png', dpi=150, bbox_inches='tight',
           facecolor='white', edgecolor='none')
plt.savefig('tabla_datos_deportivos.pdf', dpi=150, bbox_inches='tight',
           facecolor='white', edgecolor='none')
plt.close()
")

py_run_string(codigo_python)
```

Question
========

`r contexto$articulo` `r contexto$evento` se realizan cada cuatro años y en estos participan países de `r contexto$region`, alternando las sedes deportivas. La tabla muestra algunos datos de las últimas cinco versiones de `r contexto$articulo` `r contexto$evento`:

```{r mostrar_tabla, echo=FALSE, results='asis'}
# Detectar formato de salida
formatos_moodle <- c("exams2moodle", "exams2qti12", "exams2qti21", "exams2openolat")
es_moodle <- (match_exams_call() %in% formatos_moodle)

if (es_moodle) {
  # Para Moodle, usar tabla HTML simple
  cat("<table border='1' style='border-collapse: collapse; margin: 0 auto; width: 80%;'>")
  cat("<tr style='background-color: #4CAF50; color: white;'>")
  cat("<th style='padding: 12px; text-align: center; font-weight: bold;'>Año</th>")
  cat("<th style='padding: 12px; text-align: center; font-weight: bold;'>Países</th>")
  cat("<th style='padding: 12px; text-align: center; font-weight: bold;'>Deportes</th>")
  cat("<th style='padding: 12px; text-align: center; font-weight: bold;'>Total de Atletas</th>")
  cat("</tr>")

  for (i in 1:length(datos_años)) {
    año_dato <- datos_años[[i]]
    bg_color <- if(i == length(datos_años)) "#FFE082" else if(i %% 2 == 0) "#f0f0f0" else "white"
    font_weight <- if(i == length(datos_años)) "bold" else "normal"

    cat("<tr style='background-color:", bg_color, "; font-weight:", font_weight, ";'>")
    cat("<td style='padding: 10px; text-align: center;'>", año_dato$año, "</td>")
    cat("<td style='padding: 10px; text-align: center;'>", año_dato$paises, "</td>")
    cat("<td style='padding: 10px; text-align: center;'>", año_dato$deportes, "</td>")
    cat("<td style='padding: 10px; text-align: center;'>", format(año_dato$atletas, big.mark = ".", decimal.mark = ","), "</td>")
    cat("</tr>")
  }
  cat("</table>")
} else {
  # Para PDF/Word, usar imagen generada con Python
  cat("![](tabla_datos_deportivos.png){width=80%}")
}
```

Del total de atletas participantes en `r año_calculo$año`, el `r porcentaje`% compite en `r disciplina`. Para determinar el número de atletas `r if(disciplina == "natación") "nadadores" else if(disciplina == "atletismo") "atletas" else if(disciplina == "gimnasia") "gimnastas" else if(disciplina == "ciclismo") "ciclistas" else paste0("de", disciplina)` ese año, se sugiere multiplicar `r porcentaje_decimal` por el número de atletas que participaron en `r año_calculo$año`. El procedimiento sugerido es:

Answerlist
----------
- `r opciones[1]`
- `r opciones[2]`
- `r opciones[3]`
- `r opciones[4]`

Solution
========

Para resolver este problema, necesitamos evaluar si el procedimiento propuesto para calcular el número de atletas que compiten en `r disciplina` es matemáticamente correcto y suficiente.

### Análisis del procedimiento propuesto

**Datos disponibles:**
- Total de atletas en `r año_calculo$año`: `r format(año_calculo$atletas, big.mark = ".", decimal.mark = ",")`
- Porcentaje que compite en `r disciplina`: `r porcentaje`%
- Procedimiento sugerido: multiplicar `r porcentaje_decimal` por `r format(año_calculo$atletas, big.mark = ".", decimal.mark = ",")`

### Verificación matemática

El cálculo de un porcentaje de una cantidad total se realiza multiplicando el porcentaje (expresado como decimal) por la cantidad total:

**Cálculo:** `r porcentaje`% de `r format(año_calculo$atletas, big.mark = ".", decimal.mark = ",")` = `r porcentaje_decimal` × `r format(año_calculo$atletas, big.mark = ".", decimal.mark = ",")` = `r format(atletas_disciplina, big.mark = ".", decimal.mark = ",")`

Por lo tanto, aproximadamente `r format(atletas_disciplina, big.mark = ".", decimal.mark = ",")` atletas compitieron en `r disciplina` en `r año_calculo$año`.

### Evaluación de cada opción

**Análisis de la respuesta correcta:**
El procedimiento es **suficiente** porque:
1. Tenemos el dato del total de atletas en `r año_calculo$año`
2. Conocemos el porcentaje exacto que compite en `r disciplina` (`r porcentaje`%)
3. La multiplicación `r porcentaje_decimal` × `r format(año_calculo$atletas, big.mark = ".", decimal.mark = ",")` nos da directamente el número buscado
4. No se requiere información adicional para este cálculo

**Análisis de los distractores:**
- **Opciones que mencionan "insuficiente"**: Son incorrectas porque el cálculo de porcentajes no requiere información adicional como el número de países participantes o deportes
- **Opciones que mencionan usar `r porcentaje/10`**: Son incorrectas porque confunden la representación decimal del porcentaje (`r porcentaje`% = `r porcentaje_decimal`, no `r porcentaje/10`)
- **Opciones que requieren "números exactos"**: Son incorrectas porque los cálculos de porcentajes pueden dar resultados decimales que se redondean según el contexto

### Conclusión

El procedimiento sugerido (multiplicar `r porcentaje_decimal` por el total de atletas) es matemáticamente correcto y contiene toda la información necesaria para determinar el número de atletas que compitieron en `r disciplina` en `r año_calculo$año`.

Answerlist
----------
```{r generar_answerlist, echo=FALSE, results='asis'}
# Generar el answerlist basado en la posición correcta
solucion <- rep("Falso", 4)
solucion[pos_correcta] <- "Verdadero"

for(i in 1:4) {
  cat("- ", solucion[i], "\n")
}
```

Meta-information
================
exname: juegos_deportivos_interpretacion_representacion
extype: schoice
exsolution: `r paste(as.integer(solucion == "Verdadero"), collapse="")`
exshuffle: TRUE
exsection: Estadística|Análisis de datos|Porcentajes|Interpretación de tablas
exextra[Type]: Interpretación y representación
exextra[Level]: 2
exextra[Language]: es
exextra[Course]: Matemáticas ICFES
